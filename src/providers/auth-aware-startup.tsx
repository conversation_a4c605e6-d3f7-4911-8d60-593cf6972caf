'use client'

import React from 'react'
import { usePathname } from 'next/navigation'
import { StartupLoaderProvider } from '@/providers/startup-loader-provider'

export function AuthAwareStartup({ children }: { children: React.ReactNode }) {
  const pathname = usePathname()
  const isAuthRoute = React.useMemo(() => {
    if (!pathname) return false
    return (
      pathname.startsWith('/auth/signin') ||
      pathname.startsWith('/auth/signup') ||
      pathname.startsWith('/auth/forgot-password') ||
      pathname.startsWith('/auth/reset-password')
    )
  }, [pathname])

  if (isAuthRoute) {
    return <>{children}</>
  }

  return <StartupLoaderProvider>{children}</StartupLoaderProvider>
}
