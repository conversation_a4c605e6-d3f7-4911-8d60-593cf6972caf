'use client'

import { useQuery } from '@tanstack/react-query'

export interface GoogleImage {
  kind: string
  title: string
  htmlTitle: string
  link: string
  displayLink: string
  snippet: string
  htmlSnippet: string
  mime: string
  fileFormat: string
  image: {
    contextLink: string
    height: number
    width: number
    byteSize: number
    thumbnailLink: string
    thumbnailHeight: number
    thumbnailWidth: number
  }
}

export interface GoogleImagesResponse {
  items: GoogleImage[]
  searchInformation: {
    searchTime: number
    formattedSearchTime: string
    totalResults: string
    formattedTotalResults: string
  }
  queries: {
    request?: Array<{
      title: string
      totalResults: string
      searchTerms: string
      count: number
      startIndex: number
      inputEncoding: string
      outputEncoding: string
      safe: string
      cx: string
    }>
    nextPage?: Array<{
      title: string
      totalResults: string
      searchTerms: string
      count: number
      startIndex: number
      inputEncoding: string
      outputEncoding: string
      safe: string
      cx: string
    }>
  }
}

export function useGoogleImages(
  query: string,
  enabled: boolean,
  orientation: string = 'landscape', // Keep parameter for compatibility but don't use it
  page: number = 1
) {
  return useQuery({
    queryKey: ['google-images', query, page], // Remove orientation from query key
    enabled: enabled && !!query,
    queryFn: async (): Promise<GoogleImagesResponse> => {
      const params = new URLSearchParams({
        query,
        per_page: '24',
        // Remove orientation parameter - Google Custom Search API doesn't support it properly
        page: page.toString(),
      })

      const res = await fetch(`/api/media/google-images?${params}`)
      if (!res.ok) {
        const errorData = await res.json().catch(() => ({}))
        throw new Error(errorData.error || 'Failed to fetch Google images')
      }
      return res.json()
    },
    staleTime: 1000 * 60 * 10, // 10 minutes
    retry: (failureCount, error) => {
      // Don't retry on 4xx errors
      if (
        error.message.includes('Rate limit') ||
        error.message.includes('Page limit')
      ) {
        return false
      }
      return failureCount < 3
    },
  })
}
