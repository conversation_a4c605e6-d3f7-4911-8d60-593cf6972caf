'use client'

import { useQuery } from '@tanstack/react-query'

export interface GettyImage {
  id: string
  title: string
  caption?: string
  asset_family?: string
  collection_code?: string
  collection_id?: number
  collection_name?: string
  license_model?: string
  max_dimensions?: {
    height: number
    width: number
  }
  display_sizes?: Array<{
    name: string
    uri: string
    width?: number
    height?: number
    is_watermarked?: boolean
  }>
  keywords?: Array<{
    entity_uris?: string[]
    keyword_id?: string
    text: string
    type?: string
  }>
  people?: Array<{
    name: string
    silo?: string
  }>
}

export interface GettyImagesResponse {
  result_count: number
  images: GettyImage[]
}

export function useGettyImages(
  query: string,
  enabled: boolean,
  orientation: string = 'landscape', // Keep parameter for compatibility but don't use it
  page: number = 1
) {
  return useQuery({
    queryKey: ['getty-images', query, page], // Remove orientation from query key
    enabled: enabled && !!query,
    queryFn: async (): Promise<GettyImagesResponse> => {
      const params = new URLSearchParams({
        query,
        per_page: '24',
        // Remove orientation parameter - Getty Images API doesn't support it properly
        page: page.toString(),
      })

      const res = await fetch(`/api/media/getty-images?${params}`)
      if (!res.ok) {
        const errorData = await res.json().catch(() => ({}))
        throw new Error(errorData.error || 'Failed to fetch Getty images')
      }
      return res.json()
    },
    staleTime: 1000 * 60 * 10, // 10 minutes
    retry: (failureCount, error) => {
      // Don't retry on auth errors or rate limits
      if (
        error.message.includes('Authentication') ||
        error.message.includes('Rate limit')
      ) {
        return false
      }
      return failureCount < 3
    },
  })
}
