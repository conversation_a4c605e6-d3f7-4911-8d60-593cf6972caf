import { NextRequest } from 'next/server'
import {
  createCachedResponse,
  createErrorResponse,
  CACHE_DURATIONS,
} from '@/lib/api-cache'

const GCS_DEVELOPER_KEY = process.env.GCS_DEVELOPER_KEY
const GCS_CX = process.env.GCS_CX

export async function GET(request: NextRequest) {
  if (!GCS_DEVELOPER_KEY || !GCS_CX) {
    return createErrorResponse('Google Custom Search API not configured', 500)
  }

  const { searchParams } = new URL(request.url)
  const query = searchParams.get('query')
  const orientation = searchParams.get('orientation') || 'landscape'
  const perPage = searchParams.get('per_page') || '24'
  const page = searchParams.get('page') || '1'

  if (!query) {
    return createErrorResponse('Query parameter is required', 400)
  }

  // Calculate start index for pagination (Google Custom Search uses 1-based indexing)
  const startIndex = (parseInt(page) - 1) * parseInt(perPage) + 1

  // Google Custom Search API has a limit of 100 results total
  if (startIndex > 100) {
    return createErrorResponse('Page limit exceeded (max 100 results)', 400)
  }

  try {
    // Build the API URL with parameters
    const params = new URLSearchParams({
      key: GCS_DEVELOPER_KEY,
      cx: GCS_CX,
      q: query,
      searchType: 'image',
      num: Math.min(parseInt(perPage), 10).toString(), // Max 10 per request
      start: startIndex.toString(),
      safe: 'active', // Enable safe search
      rights: 'cc_publicdomain,cc_attribute,cc_sharealike,cc_noncommercial', // Free/CC licenses only
    })

    // Add image size filter based on orientation
    if (orientation === 'landscape') {
      params.append('imgSize', 'large')
      params.append('imgType', 'photo')
    } else if (orientation === 'portrait') {
      params.append('imgSize', 'large') 
      params.append('imgType', 'photo')
    } else if (orientation === 'square') {
      params.append('imgSize', 'medium')
      params.append('imgType', 'photo')
    }

    const url = `https://customsearch.googleapis.com/customsearch/v1?${params}`

    const response = await fetch(url)

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      console.error('Google Custom Search API error:', response.status, errorData)
      
      if (response.status === 429) {
        return createErrorResponse('Rate limit exceeded', 429)
      }
      
      throw new Error(`Google Custom Search API error: ${response.status}`)
    }

    const data = await response.json()

    // Transform the response to match our expected format
    const transformedData = {
      items: data.items || [],
      searchInformation: data.searchInformation || {},
      queries: data.queries || {},
    }

    return createCachedResponse(transformedData, CACHE_DURATIONS.GOOGLE_SEARCH)
  } catch (error) {
    console.error('Google Custom Search API error:', error)
    return createErrorResponse('Failed to fetch images from Google', 500)
  }
}
