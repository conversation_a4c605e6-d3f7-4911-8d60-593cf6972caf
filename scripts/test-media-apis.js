#!/usr/bin/env node

/**
 * Test script for Google Custom Search and Getty Images API integration
 * 
 * Usage: bun run scripts/test-media-apis.js
 * 
 * This script tests the new media API endpoints to ensure they work correctly
 * with proper error handling and response formatting.
 */

const BASE_URL = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'

async function testGoogleImagesAPI() {
  console.log('\n🔍 Testing Google Custom Search API...')
  
  try {
    const params = new URLSearchParams({
      query: 'nature landscape',
      orientation: 'landscape',
      per_page: '10',
      page: '1'
    })
    
    const response = await fetch(`${BASE_URL}/api/media/google-images?${params}`)
    
    if (!response.ok) {
      const error = await response.json()
      console.log(`❌ Google Images API failed: ${response.status} - ${error.error}`)
      return false
    }
    
    const data = await response.json()
    console.log(`✅ Google Images API success: Found ${data.items?.length || 0} images`)
    
    if (data.items && data.items.length > 0) {
      const firstImage = data.items[0]
      console.log(`   Sample image: ${firstImage.title} (${firstImage.image.width}x${firstImage.image.height})`)
    }
    
    return true
  } catch (error) {
    console.log(`❌ Google Images API error: ${error.message}`)
    return false
  }
}

async function testGettyImagesAPI() {
  console.log('\n🖼️  Testing Getty Images API...')
  
  try {
    const params = new URLSearchParams({
      query: 'business meeting',
      orientation: 'landscape',
      per_page: '10',
      page: '1'
    })
    
    const response = await fetch(`${BASE_URL}/api/media/getty-images?${params}`)
    
    if (!response.ok) {
      const error = await response.json()
      console.log(`❌ Getty Images API failed: ${response.status} - ${error.error}`)
      return false
    }
    
    const data = await response.json()
    console.log(`✅ Getty Images API success: Found ${data.images?.length || 0} images`)
    
    if (data.images && data.images.length > 0) {
      const firstImage = data.images[0]
      console.log(`   Sample image: ${firstImage.title} (${firstImage.collection_name})`)
    }
    
    return true
  } catch (error) {
    console.log(`❌ Getty Images API error: ${error.message}`)
    return false
  }
}

async function testPagination() {
  console.log('\n📄 Testing pagination...')
  
  try {
    // Test Google Images pagination
    const googleParams = new URLSearchParams({
      query: 'technology',
      page: '2'
    })
    
    const googleResponse = await fetch(`${BASE_URL}/api/media/google-images?${googleParams}`)
    const googleSuccess = googleResponse.ok
    
    // Test Getty Images pagination
    const gettyParams = new URLSearchParams({
      query: 'technology',
      page: '2'
    })
    
    const gettyResponse = await fetch(`${BASE_URL}/api/media/getty-images?${gettyParams}`)
    const gettySuccess = gettyResponse.ok
    
    if (googleSuccess && gettySuccess) {
      console.log('✅ Pagination works for both APIs')
      return true
    } else {
      console.log(`❌ Pagination failed - Google: ${googleSuccess}, Getty: ${gettySuccess}`)
      return false
    }
  } catch (error) {
    console.log(`❌ Pagination test error: ${error.message}`)
    return false
  }
}

async function testErrorHandling() {
  console.log('\n⚠️  Testing error handling...')
  
  try {
    // Test missing query parameter
    const response = await fetch(`${BASE_URL}/api/media/google-images`)
    
    if (response.status === 400) {
      console.log('✅ Error handling works: Missing query parameter correctly rejected')
      return true
    } else {
      console.log(`❌ Error handling failed: Expected 400, got ${response.status}`)
      return false
    }
  } catch (error) {
    console.log(`❌ Error handling test error: ${error.message}`)
    return false
  }
}

async function main() {
  console.log('🚀 Starting Media APIs Integration Test')
  console.log('=====================================')
  
  const results = []
  
  // Check if environment variables are set
  if (!process.env.GCS_DEVELOPER_KEY || !process.env.GCS_CX) {
    console.log('⚠️  Google Custom Search API credentials not configured')
    console.log('   Set GCS_DEVELOPER_KEY and GCS_CX environment variables')
  }
  
  if (!process.env.GETTY_CLIENT_ID || !process.env.GETTY_CLIENT_SECRET) {
    console.log('⚠️  Getty Images API credentials not configured')
    console.log('   Set GETTY_CLIENT_ID and GETTY_CLIENT_SECRET environment variables')
  }
  
  // Run tests
  results.push(await testGoogleImagesAPI())
  results.push(await testGettyImagesAPI())
  results.push(await testPagination())
  results.push(await testErrorHandling())
  
  // Summary
  const passed = results.filter(Boolean).length
  const total = results.length
  
  console.log('\n📊 Test Summary')
  console.log('===============')
  console.log(`Passed: ${passed}/${total}`)
  
  if (passed === total) {
    console.log('🎉 All tests passed! Media APIs integration is working correctly.')
    process.exit(0)
  } else {
    console.log('❌ Some tests failed. Please check the API configuration and try again.')
    process.exit(1)
  }
}

// Run the tests
main().catch(error => {
  console.error('💥 Test runner error:', error)
  process.exit(1)
})
